# Grafana Subpath 配置修复指南

## 错误信息
```
If you're seeing this Grafana has failed to load its application files
1. This could be caused by your reverse proxy settings.
2. If you host grafana under subpath make sure your grafana.ini root_url setting includes subpath.
```

## 问题原因
Grafana在 `/grafana/` 子路径下运行时，需要同时配置：
1. ✅ **Nginx反向代理**（已修复）
2. ❌ **Grafana服务器配置**（需要配置）

## 解决方案

### 1. Nginx配置（已完成）
- 移除了 `proxy_pass` 末尾的斜杠
- 添加了完整的代理头支持
- 移除了可能有问题的 `sub_filter`
- 添加了WebSocket和超时配置

### 2. Grafana服务器配置（需要执行）

需要在Grafana的配置中设置以下参数：

#### 方法A：修改 grafana.ini 文件
```ini
[server]
# 设置根URL包含子路径
root_url = https://tasp.trinasolar.com/grafana/
# 启用子路径服务
serve_from_sub_path = true
```

#### 方法B：使用环境变量（推荐用于容器部署）
```bash
# 在Grafana容器启动时设置环境变量
GF_SERVER_ROOT_URL=https://tasp.trinasolar.com/grafana/
GF_SERVER_SERVE_FROM_SUB_PATH=true
```

#### 方法C：Kubernetes部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
spec:
  template:
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        env:
        - name: GF_SERVER_ROOT_URL
          value: "https://tasp.trinasolar.com/grafana/"
        - name: GF_SERVER_SERVE_FROM_SUB_PATH
          value: "true"
```

### 3. Rancher部署配置
如果通过Rancher部署，在应用配置中添加环境变量：
```
GF_SERVER_ROOT_URL=https://tasp.trinasolar.com/grafana/
GF_SERVER_SERVE_FROM_SUB_PATH=true
```

## 部署步骤

### 步骤1：应用Nginx配置
```bash
# 1. 备份现有配置
cp deploy/prod/nginx.conf deploy/prod/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)

# 2. 重新部署nginx
docker build -t your-nginx-image:latest -f deploy/prod/Dockerfile .
# 重启nginx服务
```

### 步骤2：配置Grafana服务
根据你的部署方式选择上述方法A、B或C来配置Grafana。

### 步骤3：重启Grafana服务
```bash
# 重启Grafana容器或Pod
kubectl rollout restart deployment/grafana  # K8s方式
# 或者通过Rancher界面重启服务
```

## 验证步骤

1. **检查Grafana配置**
   访问 `https://tasp.trinasolar.com/grafana/api/health` 应该返回正常状态

2. **检查页面加载**
   访问 `https://tasp.trinasolar.com/grafana/` 应该正常显示登录页面

3. **检查静态资源**
   浏览器开发者工具中，静态资源应该从 `/grafana/` 路径正确加载

4. **检查WebSocket**
   登录后，实时功能应该正常工作，不再出现WebSocket错误

## 常见问题

### Q: 修改后仍然显示相同错误？
A: 确保Grafana服务已重启，配置才会生效。

### Q: 静态资源404错误？
A: 检查 `GF_SERVER_SERVE_FROM_SUB_PATH=true` 是否正确设置。

### Q: 登录后跳转到错误页面？
A: 检查 `GF_SERVER_ROOT_URL` 是否包含完整的域名和子路径。

## 关键配置总结

| 配置项 | 值 | 说明 |
|--------|-----|------|
| `GF_SERVER_ROOT_URL` | `https://tasp.trinasolar.com/grafana/` | 完整的访问URL |
| `GF_SERVER_SERVE_FROM_SUB_PATH` | `true` | 启用子路径服务 |
| nginx `proxy_pass` | `http://10.160.134.53:30002` | 不要末尾斜杠 |

配置完成后，Grafana应该能够正常在 `/grafana/` 子路径下工作。
