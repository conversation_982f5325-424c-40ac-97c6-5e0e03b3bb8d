worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;
    server_tokens off;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    gzip on;
    gzip_buffers 32 4k;
    gzip_comp_level 5;
    gzip_min_length 200;
    gzip_types text/css text/xml application/javascript;
    gzip_vary on;
    map $http_origin $modified_origin {
        ~^https://(.+)$ $1;
        default $host:$server_port;
    }
    underscores_in_headers on;
    client_header_buffer_size 1024k;        # 初始缓冲区大小
    large_client_header_buffers 100 64k;    # 最大缓冲区和数量（4个64k）
    server {
        listen       80;
        server_name  localhost;
        client_max_body_size 200M;
        #charset koi8-r;

        add_header Strict-Transport-Security "max-age=63072000" always;
        add_header Content-Security-Policy "upgrade-insecure-requests";

        #access_log  logs/host.access.log  main;
        location / {
            root /opt/sinopec;
            try_files $uri $uri/ /index.html;
            add_header  Access-Control-Allow-Origin * ;
            add_header  Access-Control-Allow-Methods *;
            add_header  Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        }


        location ~ .*\.(js|css|htm|html|gif|jpg|jpeg|png|bmp|swf|ioc|rar|zip|txt|flv|mid|doc|ppt|pdf|xls|mp3|mp4|wma|svg|otf|ttf|ttc|eot|woff|woff2|json)$ {
            add_header  Access-Control-Allow-Origin   * ;
            add_header  Access-Control-Allow-Methods *;
            add_header  Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header  Cache-Control no-cache;
            root /opt/sinopec;
        }
        # Collabora Online 静态资源代理
        location ^~ /browser/ {
            add_header Content-Security-Policy "frame-ancestors ************:* ************:* *************:* tasp.trinasolar.com:*";
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $modified_origin;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_redirect off;
           # proxy_pass http://************:9980;
           proxy_pass http://tasp-knowledge-collabora-svc-clusterip.trinasolar-platform--tasp--prod:9980;
        }
        # Collabora Online websocket代理
      #  location ^~ /cool {
      location ~ ^/cool/(.*)/ws$ {
            add_header Content-Security-Policy "frame-ancestors ************:* ************:* *************:* tasp.trinasolar.com:*";
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "Upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
            # proxy_pass http://************:9980;
            proxy_pass http://tasp-knowledge-collabora-svc-clusterip.trinasolar-platform--tasp--prod:9980;
        }

        # 应用系统知识库微前端
        location ^~ /knowledge/ {
            proxy_set_header real_ip $remote_addr;
           # proxy_pass http://************:80/knowledge/;
            proxy_pass http://tasp-knowledge-ui-svc-clusterip.trinasolar-platform--tasp--prod/knowledge/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        location /kepler/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Cookie "";
            proxy_set_header sec-ch-ua "";
            proxy_set_header sec-ch-ua-mobile "";
            proxy_set_header sec-ch-ua-platform "";
            proxy_pass http://************:8088/kepler/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        location /harmony/ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            #proxy_set_header Cookie $http_cookie;
            proxy_set_header Cookie "";
            proxy_set_header sec-ch-ua "";
            proxy_set_header sec-ch-ua-mobile "";
            proxy_set_header sec-ch-ua-platform "";
            proxy_pass http://************:8088/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }
        # 应用系统微前端
	    location ^~ /kepler-webpage/ {
            proxy_set_header real_ip $remote_addr;
            # proxy_pass http://************:80/kepler-webpage/;
            proxy_pass http://app-system-ui-svc-clusterip.trinasolar-platform--tasp--prod/kepler-webpage/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        # 应用程序微前端
        location ^~ /system/ {
            proxy_set_header real_ip $remote_addr;
            # proxy_pass http://************:80/system/;
            proxy_pass http://app-program-ui-svc-clusterip.trinasolar-platform--tasp--prod/system/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        # 代码仓库微前端
        location ^~ /efficiency/ {
            proxy_set_header real_ip $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            # proxy_ssl_verify off;
            proxy_pass http://tasp-scm-ui-svc-clusterip.trinasolar-platform--tasp--prod/efficiency/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        # 技术文档库微前端
        location ^~ /dochub/ {
            proxy_set_header real_ip $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            # proxy_ssl_verify off;
            proxy_pass http://tasp-dochub-ui-svc-clusterip.trinasolar-platform--tasp--prod/dochub/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        # apim微前端
        location ^~ /apim/ {
            proxy_set_header real_ip $remote_addr;
            proxy_set_header X-Forwarded-Proto $scheme;
            # proxy_ssl_verify off;
            proxy_pass http://tasp-apim-ui-svc-clusterip.trinasolar-platform--tasp--prod/apim/;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }

        # 知识库后端代理
        location /api/doc/ {
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Cookie $http_cookie;
                proxy_ssl_verify off;
               # proxy_pass http://************:8080/;
                proxy_pass http://************:8088/kepler/doc/;
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
        }

        location /api/ {
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Cookie $http_cookie;
                proxy_pass http://************:8088/;
                proxy_set_header Cookie "";  # 清空Cookie头
                proxy_hide_header Set-Cookie;  # 隐藏后端返回的Set-Cookie
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
            }

        # 应用程序子应用api请求转发
        location ^~  /apimiddleware/ {
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header Cookie $http_cookie;
                proxy_pass http://*************:31089/;
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
            }
        location ^~  /zeus-ui/ {
                proxy_set_header real_ip $remote_addr;
                proxy_pass http://************;
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
            }
           # grafana转发
        location ^~ /grafana/ {
            proxy_pass http://*************:30002/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            # 重写静态资源路径
            sub_filter 'src="/' 'src="/grafana/';
            sub_filter 'href="/' 'href="/grafana/';
            sub_filter_once off;
        }
        location /zeusapi/ {
                proxy_pass http://*************:31089/;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
				add_header Cache-Control no-cache;
				add_header Pragma no-cache;
        }

        location /zeususer/ {
                proxy_pass http://*************:31089/;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 智能小助手静态资源转发
        location ^~  /qkd-rag/ {
            proxy_set_header real_ip $remote_addr;
            proxy_pass http://***********;
            proxy_connect_timeout 500s;
            proxy_read_timeout 500s;
            proxy_send_timeout 500s;
        }
        # 智能小助手api路径转发
        location /rag-api/ {
            proxy_pass http://***********;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        # To allow POST on static pages 允许静态页使用POST方法
        error_page  405     =200 $uri;
        location = /50x.html {
            root   html;
        }
    }
  	server{
  		listen 80;
  		server_name _;
  		location /{
  			return 403;
  		}
  	}
}